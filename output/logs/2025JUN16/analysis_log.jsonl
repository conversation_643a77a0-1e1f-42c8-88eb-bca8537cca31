{"timestamp": "2025-06-16T00:58:10.506055", "on_task": true, "confidence": 0.95, "reason": "User is reviewing code PRs as expected", "screenshot_ref": "test.png"}
{"timestamp": "2025-06-16T00:58:29.946182", "on_task": true, "confidence": 0.95, "reason": "User is reviewing code PRs as expected", "screenshot_ref": "test.png"}
{"timestamp": "2025-06-16T01:21:41.802905", "on_task": true, "confidence": 0.95, "reason": "User is reviewing code PRs as expected", "screenshot_ref": "test.png"}
{"timestamp": "2025-06-16T01:30:30.344830", "on_task": false, "confidence": 0.85, "reason": "Browsing social media instead of working on 'Test task from Python'", "screenshot_ref": "output/screenshots/2025JUN16/013029-335937-2584.png"}
{"timestamp": "2025-06-16T01:30:51.913601", "on_task": true, "confidence": 0.95, "reason": "User is reviewing code PRs as expected", "screenshot_ref": "test.png"}
{"timestamp": "2025-06-16T01:31:30.350029", "on_task": false, "confidence": 0.85, "reason": "Browsing social media instead of working on 'Test task from Python'", "screenshot_ref": "output/screenshots/2025JUN16/013029-335937-2584.png"}
{"timestamp": "2025-06-16T01:33:55.602304", "on_task": false, "confidence": 0.85, "reason": "Browsing social media instead of working on 'Review pull request #123'", "screenshot_ref": "output/screenshots/2025JUN16/013353-754206-2686.png"}
{"timestamp": "2025-06-16T01:48:29.822279", "on_task": true, "confidence": 0.92, "reason": "User in project management tool - working on 'Review pull request #123'", "screenshot_ref": "output/screenshots/2025JUN16/014827-558682-3122.png"}
